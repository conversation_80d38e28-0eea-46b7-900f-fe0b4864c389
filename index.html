<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慧习作 - 登录页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #FFFFFF;
            width: 390px;
            height: 844px;
            margin: 0 auto;
            position: relative;
            box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
        }

        .container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        /* 状态栏 */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            width: 390px;
            height: 40px;
            background: #FFFFFF;
            border: 1px solid #F3F4F6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 12px;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .signal-icons {
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .battery-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        /* Logo */
        .logo {
            position: absolute;
            top: 303px;
            left: 95.5px;
            width: 199px;
            height: 40px;
        }

        /* 欢迎文字 */
        .welcome-text {
            position: absolute;
            top: 367px;
            left: 40.5px;
            width: 308px;
            height: 20px;
            font-family: Inter;
            font-weight: 400;
            font-size: 12px;
            line-height: 20px;
            text-align: center;
            color: #9095A0;
        }

        /* 按钮样式 */
        .login-button {
            position: absolute;
            width: 350px;
            height: 52px;
            left: 20px;
            border-radius: 26px;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-family: Inter;
            font-weight: 400;
            font-size: 18px;
            line-height: 28px;
            color: #FFFFFF;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .wechat-login {
            top: 587px;
            background: #379AE6;
            box-shadow: 0px 4px 9px 0px rgba(55, 154, 230, 0.11), 0px 0px 2px 0px rgba(55, 154, 230, 0.12);
        }

        .phone-code-login {
            top: 651px;
            background: #636AE8;
            box-shadow: 0px 4px 9px 0px rgba(99, 106, 232, 0.11), 0px 0px 2px 0px rgba(99, 106, 232, 0.12);
        }

        .phone-password-login {
            top: 715px;
            background: #7F55E0;
            box-shadow: 0px 4px 9px 0px rgba(127, 85, 224, 0.11), 0px 0px 2px 0px rgba(127, 85, 224, 0.12);
        }

        .button-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 底部指示器 */
        .bottom-indicator {
            position: absolute;
            top: 819px;
            left: 115px;
            width: 160px;
            height: 5px;
            background: #171A1F;
            border: 1px solid #BCC1CA;
            border-radius: 3px;
        }

        /* 图标样式 */
        .icon {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        /* 响应式调整 */
        @media (max-width: 390px) {
            body {
                width: 100vw;
            }

            .status-bar {
                width: 100%;
            }
        }

        /* 按钮交互效果 */
        .login-button:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .login-button:active {
            transform: translateY(0);
            opacity: 0.8;
        }

        /* 加载状态 */
        .login-button.loading {
            opacity: 0.7;
            cursor: not-allowed;
            position: relative;
        }

        .login-button.loading::after {
            content: '';
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid #FFFFFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
        }

        @keyframes spin {
            0% { transform: translateY(-50%) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: #FFFFFF;
            border-radius: 12px;
            padding: 24px;
            width: 320px;
            text-align: center;
            box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.15);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #171A1F;
            margin-bottom: 12px;
        }

        .modal-message {
            font-size: 14px;
            color: #9095A0;
            margin-bottom: 24px;
            line-height: 1.5;
        }

        .modal-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        .modal-button {
            padding: 10px 20px;
            border-radius: 6px;
            border: none;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .modal-button.primary {
            background: #379AE6;
            color: #FFFFFF;
        }

        .modal-button.secondary {
            background: #F3F4F6;
            color: #171A1F;
        }

        .modal-button:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }

        /* 加载状态 */
        .login-button.loading {
            opacity: 0.7;
            cursor: not-allowed;
        }

        .login-button.loading::after {
            content: '';
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid #FFFFFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: #FFFFFF;
            border-radius: 12px;
            padding: 24px;
            width: 320px;
            text-align: center;
            box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.15);
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #171A1F;
            margin-bottom: 12px;
        }

        .modal-message {
            font-size: 14px;
            color: #9095A0;
            margin-bottom: 24px;
            line-height: 1.5;
        }

        .modal-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        .modal-button {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .modal-button.primary {
            background: #379AE6;
            color: #FFFFFF;
        }

        .modal-button.secondary {
            background: #F3F4F6;
            color: #171A1F;
        }

        .modal-button:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <div class="signal-icons">
                    <img src="assets/images/signal-1.svg" alt="Signal 1" style="width: 8px; height: 11px;">
                    <img src="assets/images/signal-2.svg" alt="Signal 2" style="width: 3px; height: 8px;">
                    <img src="assets/images/signal-3.svg" alt="Signal 3" style="width: 9px; height: 11px;">
                    <img src="assets/images/signal-4.svg" alt="Signal 4" style="width: 5px; height: 11px;">
                </div>
            </div>
            <div class="status-right">
                <div class="battery-container">
                    <img src="assets/images/battery-outline.svg" alt="Battery Outline" style="width: 22px; height: 12px; position: absolute;">
                    <img src="assets/images/battery-fill-2.svg" alt="Battery Fill" style="width: 18px; height: 8px; margin-left: 2px;">
                </div>
            </div>
        </div>

        <!-- Logo -->
        <div class="logo">
            <img src="assets/images/logo.svg" alt="慧习作 Logo" class="icon">
        </div>

        <!-- 欢迎文字 -->
        <div class="welcome-text">欢迎使用慧习作</div>

        <!-- 微信授权登录按钮 -->
        <button class="login-button wechat-login">
            <div class="button-icon">
                <img src="assets/images/wechat-icon.svg" alt="WeChat" class="icon">
            </div>
            授权登录
        </button>

        <!-- 手机验证码登录按钮 -->
        <button class="login-button phone-code-login">
            <div class="button-icon">
                <div style="position: relative; width: 24px; height: 24px;">
                    <img src="assets/images/phone-icon-1.svg" alt="Phone" style="position: absolute; top: 2px; left: 2px; width: 20px; height: 16px;">
                    <img src="assets/images/phone-icon-2.svg" alt="Phone Screen" style="position: absolute; top: 5px; left: 7px; width: 11px; height: 9px;">
                    <img src="assets/images/phone-icon-3.svg" alt="Phone Dot" style="position: absolute; top: 8px; left: 11px; width: 2px; height: 2px;">
                    <img src="assets/images/phone-icon-4.svg" alt="Phone Antenna" style="position: absolute; top: 9px; left: 14px; width: 9px; height: 15px;">
                    <img src="assets/images/phone-icon-5.svg" alt="Phone Signal" style="position: absolute; top: 17px; left: 14px; width: 9px; height: 3px;">
                </div>
            </div>
            手机验证码
        </button>

        <!-- 手机密码登录按钮 -->
        <button class="login-button phone-password-login">
            <div class="button-icon">
                <div style="position: relative; width: 24px; height: 24px;">
                    <img src="assets/images/lock-icon-1.svg" alt="Lock Body" style="position: absolute; top: 3px; left: 5px; width: 16px; height: 21px;">
                    <img src="assets/images/lock-icon-2.svg" alt="Lock Keyhole" style="position: absolute; top: 17px; left: 12px; width: 9px; height: 7px;">
                    <img src="assets/images/lock-icon-3.svg" alt="Lock Key" style="position: absolute; top: 13px; left: 14px; width: 6px; height: 7px;">
                </div>
            </div>
            手机密码
        </button>

        <!-- 底部指示器 -->
        <div class="bottom-indicator"></div>

        <!-- 弹窗模态框 -->
        <div id="loginModal" class="modal">
            <div class="modal-content">
                <div class="modal-title" id="modalTitle">登录提示</div>
                <div class="modal-message" id="modalMessage">正在处理您的登录请求...</div>
                <div class="modal-buttons">
                    <button class="modal-button secondary" onclick="closeModal()">取消</button>
                    <button class="modal-button primary" onclick="confirmLogin()">确认</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 登录按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const wechatBtn = document.querySelector('.wechat-login');
            const phoneCodeBtn = document.querySelector('.phone-code-login');
            const phonePasswordBtn = document.querySelector('.phone-password-login');

            // 微信登录
            wechatBtn.addEventListener('click', function() {
                handleLogin('wechat', '微信授权登录', '将跳转到微信进行授权登录，请确认继续？');
            });

            // 手机验证码登录
            phoneCodeBtn.addEventListener('click', function() {
                handleLogin('phoneCode', '手机验证码登录', '请输入您的手机号码，我们将发送验证码到您的手机。');
            });

            // 手机密码登录
            phonePasswordBtn.addEventListener('click', function() {
                handleLogin('phonePassword', '手机密码登录', '请输入您的手机号码和密码进行登录。');
            });
        });

        let currentLoginType = '';

        function handleLogin(type, title, message) {
            currentLoginType = type;
            showModal(title, message);

            // 添加加载状态
            const button = document.querySelector(`.${type === 'wechat' ? 'wechat-login' : type === 'phoneCode' ? 'phone-code-login' : 'phone-password-login'}`);
            button.classList.add('loading');

            // 模拟加载延迟
            setTimeout(() => {
                button.classList.remove('loading');
            }, 2000);
        }

        function showModal(title, message) {
            const modal = document.getElementById('loginModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalMessage = document.getElementById('modalMessage');

            modalTitle.textContent = title;
            modalMessage.textContent = message;
            modal.style.display = 'flex';
        }

        function closeModal() {
            const modal = document.getElementById('loginModal');
            modal.style.display = 'none';
            currentLoginType = '';
        }

        function confirmLogin() {
            switch(currentLoginType) {
                case 'wechat':
                    // 模拟微信登录跳转
                    alert('正在跳转到微信授权页面...');
                    break;
                case 'phoneCode':
                    // 跳转到验证码页面
                    window.location.href = 'login.html';
                    break;
                case 'phonePassword':
                    // 模拟跳转到密码登录页面
                    alert('正在跳转到手机密码登录页面...');
                    break;
            }
            closeModal();
        }

        // 点击模态框外部关闭
        document.getElementById('loginModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 添加按钮点击反馈效果
        document.querySelectorAll('.login-button').forEach(button => {
            button.addEventListener('mousedown', function() {
                this.style.transform = 'translateY(1px)';
            });

            button.addEventListener('mouseup', function() {
                this.style.transform = 'translateY(-2px)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
