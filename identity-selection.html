<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多身份登录 - 慧习作</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #FFFFFF;
            width: 390px;
            height: 844px;
            position: relative;
            margin: 0 auto;
            box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
        }

        /* 状态栏 */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            width: 390px;
            height: 40px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-bar-left {
            width: 72px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .status-bar-right {
            width: 96px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 返回按钮 */
        .back-button {
            position: absolute;
            top: 52px;
            left: 33px;
            width: 24px;
            height: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 标题 */
        .title {
            position: absolute;
            top: 173px;
            left: 33px;
            width: 100px;
            height: 30px;
            font-family: 'Archivo', sans-serif;
            font-weight: 400;
            font-size: 20px;
            line-height: 30px;
            color: #9095A0;
        }

        /* 身份选择按钮 */
        .identity-button {
            position: absolute;
            width: 248px;
            height: 52px;
            left: 71px;
            border-radius: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .teacher-button {
            top: 336px;
            background: #379AE6;
            border: 1px solid transparent;
            box-shadow: 0px 4px 9px 0px rgba(55, 154, 230, 0.11), 0px 0px 2px 0px rgba(55, 154, 230, 0.12);
        }

        .parent-button {
            top: 419px;
            background: #FFFFFF;
            border: 1px solid #379AE6;
            box-shadow: 0px 4px 9px 0px rgba(55, 154, 230, 0.11), 0px 0px 2px 0px rgba(55, 154, 230, 0.12);
        }

        .button-text {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 18px;
            line-height: 28px;
            margin-left: 8px;
        }

        .teacher-button .button-text {
            color: #FFFFFF;
        }

        .parent-button .button-text {
            color: #379AE6;
        }

        .button-icon {
            width: 24px;
            height: 24px;
            position: relative;
        }

        /* 确定按钮 */
        .confirm-button {
            position: absolute;
            top: 746px;
            left: 20px;
            width: 350px;
            height: 52px;
            background: #636AE8;
            border: 1px solid transparent;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .confirm-button-text {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 18px;
            line-height: 28px;
            color: #FFFFFF;
        }

        /* 图标样式 */
        .icon {
            display: block;
        }

        /* 状态栏图标组合 */
        .status-icons-left {
            position: relative;
            width: 27.34px;
            height: 10.7px;
            margin-left: 30px;
            margin-top: 16.79px;
        }

        .status-icons-right {
            position: relative;
            width: 65.87px;
            height: 10.56px;
            margin-left: 12px;
            margin-top: 16.67px;
        }

        /* 选中状态 */
        .identity-button.selected.teacher-button {
            background: #379AE6;
        }

        .identity-button.selected.parent-button {
            background: #379AE6;
            border: 1px solid #379AE6;
        }

        .identity-button.selected.parent-button .button-text {
            color: #FFFFFF;
        }

        /* 未选中状态 */
        .identity-button:not(.selected).teacher-button {
            background: #FFFFFF;
            border: 1px solid #379AE6;
        }

        .identity-button:not(.selected).teacher-button .button-text {
            color: #379AE6;
        }

        /* 交互效果 */
        .identity-button:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .confirm-button:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .back-button:hover {
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="status-bar-left">
            <div class="status-icons-left">
                <!-- 按照Figma精确位置定位 -->
                <img src="images/status-bar-icon1.svg" alt="" style="position: absolute; left: 0; top: 0; width: 7.86px; height: 10.7px;">
                <img src="images/status-bar-icon2.svg" alt="" style="position: absolute; left: 9.59px; top: 1.61px; width: 2.25px; height: 7.47px;">
                <img src="images/status-bar-icon3.svg" alt="" style="position: absolute; left: 13.53px; top: 0.25px; width: 8.1px; height: 10.19px;">
                <img src="images/status-bar-icon4.svg" alt="" style="position: absolute; left: 22.86px; top: 0.25px; width: 4.48px; height: 10.19px;">
            </div>
        </div>
        <div class="status-bar-right">
            <div class="status-icons-right">
                <!-- 按照Figma精确位置定位 -->
                <img src="images/status-bar-right-icon1.svg" alt="" style="position: absolute; left: 43.05px; top: 0; width: 20.28px; height: 10.06px;">
                <img src="images/status-bar-right-icon2.svg" alt="" style="position: absolute; left: 64.68px; top: 3.76px; width: 1.19px; height: 3.59px;">
                <img src="images/status-bar-right-icon3.svg" alt="" style="position: absolute; left: 44.26px; top: 1.2px; width: 17.87px; height: 7.66px;">
                <img src="images/status-bar-right-icon4.svg" alt="" style="position: absolute; left: 22.13px; top: 0.35px; width: 14.47px; height: 10.07px;">
                <img src="images/status-bar-right-icon5.svg" alt="" style="position: absolute; left: 8.51px; top: 2.05px; width: 2.55px; height: 8.51px;">
                <img src="images/status-bar-right-icon6.svg" alt="" style="position: absolute; left: 12.77px; top: 0.35px; width: 2.55px; height: 10.21px;">
                <img src="images/status-bar-right-icon7.svg" alt="" style="position: absolute; left: 4.26px; top: 5.03px; width: 2.55px; height: 5.53px;">
                <img src="images/status-bar-right-icon8.svg" alt="" style="position: absolute; left: 0; top: 7.16px; width: 2.55px; height: 3.4px;">
            </div>
        </div>
    </div>

    <!-- 返回按钮 -->
    <div class="back-button" onclick="goBack()">
        <!-- 按照Figma精确位置定位 -->
        <img src="images/arrow-left.svg" alt="返回" style="position: absolute; left: 3.4px; top: 11.98px; width: 17.2px; height: 0;">
        <img src="images/arrow-left-2.svg" alt="返回" style="position: absolute; left: 3.42px; top: 5.98px; width: 6.02px; height: 12.04px;">
    </div>

    <!-- 标题 -->
    <div class="title">请选择身份</div>

    <!-- 教师按钮 -->
    <div class="identity-button teacher-button selected" onclick="selectIdentity('teacher')">
        <div class="button-icon">
            <!-- 按照Figma精确位置定位教师头像 -->
            <img src="images/teacher-face.svg" alt="教师" style="position: absolute; top: 2px; left: 0.18px; width: 23.63px; height: 22px;">
            <img src="images/teacher-eye1.svg" alt="" style="position: absolute; top: 11.75px; left: 7.75px; width: 2.5px; height: 2.5px;">
            <img src="images/teacher-eye2.svg" alt="" style="position: absolute; top: 11.75px; left: 13.75px; width: 2.5px; height: 2.5px;">
        </div>
        <span class="button-text">教师</span>
    </div>

    <!-- 家长按钮 -->
    <div class="identity-button parent-button" onclick="selectIdentity('parent')">
        <div class="button-icon">
            <!-- 按照Figma精确位置定位家长头像 -->
            <img src="images/parent-face-full.svg" alt="家长" style="position: absolute; top: 0; left: 0; width: 24px; height: 24px;">
            <img src="images/parent-eye1.svg" alt="" style="position: absolute; top: 12.75px; left: 7.75px; width: 2.5px; height: 2.5px;">
            <img src="images/parent-eye2.svg" alt="" style="position: absolute; top: 12.75px; left: 13.75px; width: 2.5px; height: 2.5px;">
        </div>
        <span class="button-text">家长</span>
    </div>

    <!-- 确定按钮 -->
    <div class="confirm-button" onclick="confirmSelection()">
        <span class="confirm-button-text">确定</span>
    </div>

    <script>
        let selectedIdentity = 'teacher'; // 默认选中教师

        function selectIdentity(identity) {
            // 移除所有选中状态
            document.querySelectorAll('.identity-button').forEach(btn => {
                btn.classList.remove('selected');
            });

            // 添加选中状态
            if (identity === 'teacher') {
                document.querySelector('.teacher-button').classList.add('selected');
                selectedIdentity = 'teacher';
            } else {
                document.querySelector('.parent-button').classList.add('selected');
                selectedIdentity = 'parent';
            }
        }

        function confirmSelection() {
            if (selectedIdentity === 'teacher') {
                alert('已选择教师身份，即将跳转到教师登录页面');
                // 这里可以跳转到教师登录页面
                // window.location.href = 'teacher-login.html';
            } else {
                alert('已选择家长身份，即将跳转到家长登录页面');
                // 这里可以跳转到家长登录页面
                // window.location.href = 'parent-login.html';
            }
        }

        function goBack() {
            // 返回上一页
            window.history.back();
        }

        // 添加按钮点击效果
        document.querySelectorAll('.identity-button, .confirm-button, .back-button').forEach(button => {
            button.addEventListener('mousedown', function() {
                this.style.transform = 'translateY(1px)';
            });

            button.addEventListener('mouseup', function() {
                this.style.transform = 'translateY(-2px)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
